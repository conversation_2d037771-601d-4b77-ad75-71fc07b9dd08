"use client";

import { Card } from "@mui/material";
import EditButton from "@/_core/ui/components/button/editButton";
import { ReactNode } from "react";
import { Step, FieldMeta } from "@/_core/ui/components/forms/types";

type Props = {
  children: ReactNode;
};

const meta = {
  apellido: {
    label: "Apellido",
    type: "text",
  },
  nombre: {
    label: "Nombre",
    type: "text",
  },
} as Record<string, FieldMeta>;

const steps = [
  {
    label: "Datos personales",
    fields: ["apellido", "nombre"],
  },
] as Step<"asociados", "edit">[];

export default function EditableTab({ children }: Props) {
  return (
    <Card className="
      relative 
      p-6 
      w-full h-full
      lg:min-h-50"
    >
      <div
        className="
        flex flex-row
        justify-between
      "
      >
        {children}
        <div className="top-4 right-4 pl-4">
          <EditButton entityName="asociados" entityId="1" meta={meta} steps={steps} label="Editar" />
        </div>
      </div>
    </Card>
  );
}
