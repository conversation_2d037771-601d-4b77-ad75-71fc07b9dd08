
"use client";

import { PatchEditRenderer } from "../CRUD/patchEdit/patchEditRenderer";
import { EntityKeys } from "@/_lib/utils/entities";

import { IconButton, Typography } from "@mui/material";
import { Edit2 } from "react-feather";

import { Step, FieldMeta } from "../forms/types";

type Props = {
  entityName: EntityKeys;
  entityId: string;
  size?: "small" | "medium" | "large";
  actionName?: string;
  label?: string;
  meta: Record<string, FieldMeta>;
  steps?: Step<EntityKeys, string>[];
};

export default function EditButton({
  entityName,
  entityId,
  size = "small",
  actionName = "edit",
  label = "Editar",
  meta,
  steps,
}: Props ) {
  const iconSize = size === "small" ? 18 : size === "medium" ? 20 : 24;

  return (
    <IconButton onClick={() => <PatchEditRenderer entityName={entityName} actionName={actionName} meta={meta} steps={steps} label={label} entityId={entityId} />}>
      <Edit2 size={iconSize} />
      <Typography
        className="ml-2"
        sx={{ typography: { xs: "body2", md: "body1xl", lg: "body1xl" } }}
      >
        Editar
      </Typography>
    </IconButton>
  );
}

