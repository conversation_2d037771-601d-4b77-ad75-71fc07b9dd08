
"use client";

import { Client<PERSON>ormWrapper } from "@/_core/ui/components/forms/clientFormWrapper";
import { FieldMeta } from "@/_core/ui/components/forms/types";
import { InferSchema, EntityKeys, ActionKeys } from "@/_lib/data/model/schema";
import { FormValidateAndSubmitButton } from "@/_core/ui/components/forms/formValidateAndSubmitButton";

import { Step } from "@/_core/ui/components/forms/types";

import { Box, Typography, Stack, Modal } from "@mui/material";

type PatchEditRendererProps<E extends EntityKeys, A extends ActionKeys<E>> = {
  entityName: E;
  entityId: string | number;
  actionName?: A;
  meta: Record<keyof InferSchema<E, A>, FieldMeta>;
  steps?: Step<E, A>[];
  label: string;
};

export function PatchEditRenderer<
  E extends EntityKeys,
  A extends ActionKeys<E>
>({
  entityName,
  actionName = "edit" as A,
  meta,
  steps,
  label,
}: PatchEditRendererProps<E, A>) {
  return (
    <Modal open={true}>
      <ClientFormWrapper
        entityName={entityName}
        actionName={actionName}
        meta={meta}
        steps={steps}
        header={(
          <>
            <Stack direction="row" justifyContent="space-between" alignItems={"center"} className="mb-1">
              <Typography
                color="primary.main"
                sx={{ typography: { xs: "h5", md: "h5", lg: "h5xl" } }}
              >
                  {label}
              </Typography>
              <FormValidateAndSubmitButton label="Guardar" />
            </Stack>
          </>
        )}
      >
      </ClientFormWrapper>
    </Modal>
  );
}
